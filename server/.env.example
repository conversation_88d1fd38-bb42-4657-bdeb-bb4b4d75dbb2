# Environment Configuration
NODE_ENV=development
PORT=8080

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_role_key
SUPABASE_ANON_KEY=your_supabase_anon_key

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=7d

# SMS OTP Configuration (2Factor.in)
TWOFACTOR_API_KEY=your_2factor_api_key

# Mock OTP Configuration
MOCK_OTP=false
MOCK_OTP_VALUE=123456

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:8081
