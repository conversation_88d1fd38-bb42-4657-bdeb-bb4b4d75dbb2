# Environment Setup Guide

This document describes the environment configuration for the Ozgaar platform integration between server and web applications.

## Environment Files Structure

```
.env.shared          # Shared variables across server and web
server/.env          # Server-specific variables
web/.env             # Web-specific variables
```

## Required Environment Variables

### .env.shared (Shared Variables)
```bash
# Supabase Configuration (Shared)
SUPABASE_URL=https://plqxilygqrxtfpcmmuut.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# API Configuration
API_BASE_URL=http://localhost:3000/api
SOCKET_IO_URL=http://localhost:3000

# Application URLs
SERVER_BASE_URL=http://localhost:3000
WEB_BASE_URL=http://localhost:3001

# Regional Configuration
DEFAULT_COUNTRY_CODE=+91
SUPPORTED_LANGUAGES=hindi,english,tamil,telugu,bengali,marathi,gujarati,kannada

# Development Configuration
NODE_ENV=development
```

### server/.env (Server-Specific)
```bash
# Server Configuration
PORT=3000
HOST=localhost

# Supabase Configuration (Server-side only - sensitive keys)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 2Factor.in SMS Configuration (Primary Provider)
TWOFACTOR_API_KEY=0190ebf4-7a8d-11f0-a562-0200cd936042

# Redis Configuration
REDIS_URL=redis://default:*******@redis-16684.c301.ap-south-1-1.ec2.redns.redis-cloud.com:16684

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
OTP_RATE_LIMIT_ATTEMPTS=3
OTP_RATE_LIMIT_WINDOW_HOURS=1

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN=*
TRUST_PROXY=false

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Database Configuration
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_TIMEOUT=30000
```

### web/.env (Web-Specific)
```bash
# Next.js Public Environment Variables (exposed to browser)
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
NEXT_PUBLIC_SOCKET_IO_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=https://plqxilygqrxtfpcmmuut.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_DEFAULT_COUNTRY_CODE=+91

# Web Application Configuration
PORT=3001
HOST=localhost

# Development Configuration
NODE_ENV=development
```

## Development Scripts

### Start Both Server and Web
```bash
# Start server and web concurrently for integration testing
pnpm dev:all

# Alternative command (same as above)
pnpm dev:integration
```

### Individual Services
```bash
# Start server only
pnpm dev:server

# Start web only
pnpm dev:web

# Start mobile (existing)
pnpm dev:mobile

# Start backend (existing)
pnpm dev:backend
```

### Build Commands
```bash
# Build all (types + server + web)
pnpm build

# Build individual components
pnpm build:types
pnpm build:server
pnpm build:web
```

## Health Check Endpoints

Once both services are running:

- **Server Health**: http://localhost:3000/api/health
- **Web Application**: http://localhost:3001
- **Auth Endpoint**: http://localhost:3000/api/auth/me

## Port Configuration

- **Server**: 3000
- **Web**: 3001
- **Mobile Metro**: 8081 (existing)
- **Backend**: 8080 (existing)

## Integration Testing

1. Start both services: `pnpm dev:all`
2. Verify server health: `curl http://localhost:3000/api/health`
3. Verify web loads: Open http://localhost:3001
4. Test auth flow: Navigate to web auth pages and verify API calls to server

## Environment Variable Loading

- Server loads: `.env.shared` + `server/.env`
- Web loads: `.env.shared` + `web/.env`
- Next.js automatically loads environment files in this order:
  1. `.env.local`
  2. `.env.development` (when NODE_ENV=development)
  3. `.env`

## Security Notes

- **NEVER** commit actual API keys or secrets to version control
- Use `.env.local` for local overrides (already in .gitignore)
- Server-side keys (like SUPABASE_SERVICE_ROLE_KEY) should never be exposed to the browser
- Use NEXT_PUBLIC_ prefix only for variables that are safe to expose to the browser
