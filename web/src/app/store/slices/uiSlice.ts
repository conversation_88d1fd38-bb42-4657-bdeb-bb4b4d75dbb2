/**
 * UI Slice
 * UI state management for notifications, modals, and global UI state
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Toast notification interface
interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // in milliseconds, 0 means persistent
}

// UI state interface
interface UIState {
  // Toast notifications
  toasts: Toast[];
  
  // Loading states
  globalLoading: boolean;
  
  // Modal states
  modals: {
    [key: string]: boolean;
  };
  
  // Network status
  isOnline: boolean;
  
  // Theme and preferences
  theme: 'light' | 'dark' | 'system';
  sidebarOpen: boolean;
}

// Initial state
const initialState: UIState = {
  toasts: [],
  globalLoading: false,
  modals: {},
  isOnline: true,
  theme: 'system',
  sidebarOpen: false,
};

// UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Toast management
    addToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {
      const toast: Toast = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        duration: 5000, // Default 5 seconds
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    
    removeToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },
    
    clearAllToasts: (state) => {
      state.toasts = [];
    },
    
    // Convenience toast actions
    showSuccessToast: (state, action: PayloadAction<{ title: string; message?: string }>) => {
      const toast: Toast = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: 'success',
        duration: 4000,
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    
    showErrorToast: (state, action: PayloadAction<{ title: string; message?: string }>) => {
      const toast: Toast = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: 'error',
        duration: 6000,
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    
    showWarningToast: (state, action: PayloadAction<{ title: string; message?: string }>) => {
      const toast: Toast = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: 'warning',
        duration: 5000,
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    
    showInfoToast: (state, action: PayloadAction<{ title: string; message?: string }>) => {
      const toast: Toast = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: 'info',
        duration: 4000,
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    
    // Global loading
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload;
    },
    
    // Modal management
    openModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = true;
    },
    
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = false;
    },
    
    toggleModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = !state.modals[action.payload];
    },
    
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key] = false;
      });
    },
    
    // Network status
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    
    // Theme
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    
    // Sidebar
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
  },
});

// Export actions
export const {
  addToast,
  removeToast,
  clearAllToasts,
  showSuccessToast,
  showErrorToast,
  showWarningToast,
  showInfoToast,
  setGlobalLoading,
  openModal,
  closeModal,
  toggleModal,
  closeAllModals,
  setOnlineStatus,
  setTheme,
  setSidebarOpen,
  toggleSidebar,
} = uiSlice.actions;

// Selectors
export const selectToasts = (state: { ui: UIState }) => state.ui.toasts;
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectIsModalOpen = (modalName: string) => (state: { ui: UIState }) => 
  state.ui.modals[modalName] || false;
export const selectIsOnline = (state: { ui: UIState }) => state.ui.isOnline;
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen;

export default uiSlice.reducer;
