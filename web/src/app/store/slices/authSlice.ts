/**
 * Auth Slice
 * Authentication state management with token handling
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { UserProfile } from '@ozgaar/types';

// Auth state interface
interface AuthState {
  // Authentication status
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // User data
  user: UserProfile | null;
  
  // Tokens
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiresAt: string | null;
  
  // Error handling
  error: string | null;
  
  // OTP flow state
  otpFlow: {
    phone: string | null;
    isVerifying: boolean;
    remainingAttempts: number;
    expiresAt: string | null;
  };
}

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: false,
  user: null,
  accessToken: null,
  refreshToken: null,
  tokenExpiresAt: null,
  error: null,
  otpFlow: {
    phone: null,
    isVerifying: false,
    remainingAttempts: 0,
    expiresAt: null,
  },
};

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },
    
    // Set error
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Set tokens
    setTokens: (state, action: PayloadAction<{
      accessToken: string;
      refreshToken: string;
      expiresIn: string;
    }>) => {
      const { accessToken, refreshToken, expiresIn } = action.payload;
      
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      
      // Calculate expiration time
      const expiresInMs = parseInt(expiresIn) * 1000;
      state.tokenExpiresAt = new Date(Date.now() + expiresInMs).toISOString();
      
      state.isAuthenticated = true;
      state.error = null;
    },
    
    // Set user data
    setUser: (state, action: PayloadAction<UserProfile>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    
    // Login success
    loginSuccess: (state, action: PayloadAction<{
      user: UserProfile;
      accessToken: string;
      refreshToken: string;
      expiresIn: string;
    }>) => {
      const { user, accessToken, refreshToken, expiresIn } = action.payload;
      
      state.user = user;
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      
      // Calculate expiration time
      const expiresInMs = parseInt(expiresIn) * 1000;
      state.tokenExpiresAt = new Date(Date.now() + expiresInMs).toISOString();
      
      state.isAuthenticated = true;
      state.isLoading = false;
      state.error = null;
      
      // Clear OTP flow
      state.otpFlow = initialState.otpFlow;
    },
    
    // Logout
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.tokenExpiresAt = null;
      state.error = null;
      state.isLoading = false;
      state.otpFlow = initialState.otpFlow;
    },
    
    // OTP flow actions
    startOtpFlow: (state, action: PayloadAction<{
      phone: string;
      expiresAt: string;
      remainingAttempts: number;
    }>) => {
      const { phone, expiresAt, remainingAttempts } = action.payload;
      
      state.otpFlow = {
        phone,
        isVerifying: false,
        remainingAttempts,
        expiresAt,
      };
      state.error = null;
    },
    
    setOtpVerifying: (state, action: PayloadAction<boolean>) => {
      state.otpFlow.isVerifying = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },
    
    updateOtpAttempts: (state, action: PayloadAction<number>) => {
      state.otpFlow.remainingAttempts = action.payload;
    },
    
    clearOtpFlow: (state) => {
      state.otpFlow = initialState.otpFlow;
    },
  },
});

// Export actions
export const {
  setLoading,
  setError,
  clearError,
  setTokens,
  setUser,
  loginSuccess,
  logout,
  startOtpFlow,
  setOtpVerifying,
  updateOtpAttempts,
  clearOtpFlow,
} = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectAccessToken = (state: { auth: AuthState }) => state.auth.accessToken;
export const selectIsLoading = (state: { auth: AuthState }) => state.auth.isLoading;
export const selectError = (state: { auth: AuthState }) => state.auth.error;
export const selectOtpFlow = (state: { auth: AuthState }) => state.auth.otpFlow;

// Check if token is expired
export const selectIsTokenExpired = (state: { auth: AuthState }) => {
  const { tokenExpiresAt } = state.auth;
  if (!tokenExpiresAt) return true;
  
  return new Date() >= new Date(tokenExpiresAt);
};

export default authSlice.reducer;
