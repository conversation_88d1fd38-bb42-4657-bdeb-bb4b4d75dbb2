/**
 * RTK Query Base API Configuration
 * Centralized API configuration with authentication and error handling
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import type { ApiResponse, ApiError } from '@ozgaar/types';
import { RootState } from '../store';

// Enhanced base query with auth and refresh token handling
const baseQuery = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api',
  prepareHeaders: (headers, { getState }) => {
    // Get token from auth state
    const token = (getState() as RootState).auth.accessToken;
    
    // Set default headers
    headers.set('Content-Type', 'application/json');
    headers.set('Accept', 'application/json');
    
    // Add auth header if token exists
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
    
    return headers;
  },
});

// Enhanced base query with automatic token refresh
const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  // Attempt the original request
  let result = await baseQuery(args, api, extraOptions);
  
  // If we get a 401, try to refresh the token
  if (result.error && result.error.status === 401) {
    const state = api.getState() as RootState;
    const refreshToken = state.auth.refreshToken;
    
    if (refreshToken) {
      // Try to refresh the token
      const refreshResult = await baseQuery(
        {
          url: '/auth/refresh-token',
          method: 'POST',
          body: { refreshToken },
        },
        api,
        extraOptions
      );
      
      if (refreshResult.data) {
        // Extract new tokens from response
        const refreshData = refreshResult.data as ApiResponse<{
          accessToken: string;
          refreshToken: string;
          expiresIn: string;
        }>;
        
        if (refreshData.success && refreshData.data) {
          // Update tokens in store
          api.dispatch({
            type: 'auth/setTokens',
            payload: {
              accessToken: refreshData.data.accessToken,
              refreshToken: refreshData.data.refreshToken,
              expiresIn: refreshData.data.expiresIn,
            },
          });
          
          // Retry the original request with new token
          result = await baseQuery(args, api, extraOptions);
        } else {
          // Refresh failed, logout user
          api.dispatch({ type: 'auth/logout' });
        }
      } else {
        // Refresh failed, logout user
        api.dispatch({ type: 'auth/logout' });
      }
    } else {
      // No refresh token, logout user
      api.dispatch({ type: 'auth/logout' });
    }
  }
  
  return result;
};

// Create the base API
export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'User',
    'WorkerProfile', 
    'PosterProfile',
    'Job',
    'Application',
    'Message',
    'Notification',
    'Payment',
    'Review',
    'Persona',
  ],
  endpoints: () => ({}),
});

// Export hooks for usage in functional components
export const { 
  // These will be populated by individual API slices
} = baseApi;

// Helper function to handle API errors consistently
export const handleApiError = (error: FetchBaseQueryError | undefined): string => {
  if (!error) return 'An unknown error occurred';

  if ('status' in error) {
    // FetchBaseQueryError with status
    if (error.status === 'FETCH_ERROR') {
      return 'Network error. Please check your connection.';
    }

    if (error.status === 'PARSING_ERROR') {
      return 'Invalid response from server.';
    }

    if (error.status === 'TIMEOUT_ERROR') {
      return 'Request timeout. Please try again.';
    }

    if (error.status === 'CUSTOM_ERROR') {
      return error.error || 'Custom error occurred';
    }

    if (typeof error.status === 'number') {
      const errorData = error.data as ApiError;
      return errorData?.message || `Server error (${error.status})`;
    }
  }

  return 'An unexpected error occurred';
};

// Type-safe API response transformer
export const transformApiResponse = <T>(response: ApiResponse<T>): T => {
  if (!response.success) {
    throw new Error(response.error || response.message || 'API request failed');
  }
  
  if (!response.data) {
    throw new Error('No data received from API');
  }
  
  return response.data;
};

export default baseApi;
