/**
 * Auth API Endpoints
 * RTK Query endpoints for authentication
 */

import { baseApi, transformApiResponse } from './baseApi';
import type { 
  ApiResponse,
  SendOtpRequest,
  SendOtpResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  UserProfile
} from '@ozgaar/types';

// Auth API slice
export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Send OTP
    sendOtp: builder.mutation<SendOtpResponse, SendOtpRequest>({
      query: (body) => ({
        url: '/auth/send-otp',
        method: 'POST',
        body,
      }),
      transformResponse: (response: ApiResponse<SendOtpResponse>) => 
        transformApiResponse(response),
    }),
    
    // Verify OTP
    verifyOtp: builder.mutation<VerifyOtpResponse, VerifyOtpRequest>({
      query: (body) => ({
        url: '/auth/verify-otp',
        method: 'POST',
        body,
      }),
      transformResponse: (response: ApiResponse<VerifyOtpResponse>) => 
        transformApiResponse(response),
      invalidatesTags: ['User'],
    }),
    
    // Resend OTP
    resendOtp: builder.mutation<SendOtpResponse, { phone: string }>({
      query: (body) => ({
        url: '/auth/resend-otp',
        method: 'POST',
        body,
      }),
      transformResponse: (response: ApiResponse<SendOtpResponse>) => 
        transformApiResponse(response),
    }),
    
    // Get current user
    getCurrentUser: builder.query<UserProfile, void>({
      query: () => '/auth/me',
      transformResponse: (response: ApiResponse<UserProfile>) => 
        transformApiResponse(response),
      providesTags: ['User'],
    }),
    
    // Refresh token
    refreshToken: builder.mutation<RefreshTokenResponse, RefreshTokenRequest>({
      query: (body) => ({
        url: '/auth/refresh-token',
        method: 'POST',
        body,
      }),
      transformResponse: (response: ApiResponse<RefreshTokenResponse>) => 
        transformApiResponse(response),
    }),
    
    // Logout
    logout: builder.mutation<{ success: boolean }, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      transformResponse: (response: ApiResponse<{ success: boolean }>) => 
        transformApiResponse(response),
      invalidatesTags: ['User'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useSendOtpMutation,
  useVerifyOtpMutation,
  useResendOtpMutation,
  useGetCurrentUserQuery,
  useRefreshTokenMutation,
  useLogoutMutation,
} = authApi;
