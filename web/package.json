{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@ozgaar/types": "workspace:*", "@playwright/test": "^1.55.0", "@reduxjs/toolkit": "^2.5.0", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}