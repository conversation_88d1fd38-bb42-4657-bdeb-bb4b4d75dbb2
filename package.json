{"name": "ozgaar-monorepo", "private": true, "scripts": {"build:types": "tsc -p packages/types/tsconfig.json", "build:server": "cd server && pnpm build", "build:web": "cd web && pnpm build", "build:backend": "cd backend && pnpm build", "build": "pnpm build:types && pnpm build:server && pnpm build:web", "android": "cd mobile && pnpm android", "dev:mobile": "cd mobile && pnpm start", "dev:server": "cd server && pnpm dev", "dev:web": "cd web && pnpm dev", "dev:backend": "cd backend && pnpm dev", "dev:all": "concurrently --names \"SERVER,WEB\" --prefix-colors \"blue,green\" \"pnpm dev:server\" \"pnpm dev:web\"", "dev:integration": "pnpm dev:all", "dev": "pnpm dev:mobile & pnpm dev:backend", "dev:android": "pnpm dev:backend & pnpm android", "ts-compile": "npx tsc && npx tsc -p packages/types/tsconfig.json && npx tsc -p mobile/tsconfig.json && npx tsc -p backend/tsconfig.json && npx tsc -p server/tsconfig.json && npx tsc -p web/tsconfig.json"}, "devDependencies": {"concurrently": "^9.2.1", "typescript": "^5.9.2"}}